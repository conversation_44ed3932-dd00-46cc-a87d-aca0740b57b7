/* Import focus styles for accessibility */
@import './components/ui/focus-styles.css';

/* Base font system */
:root {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 173 80% 40%;
    --primary-foreground: 210 40% 98%;

    --secondary: 260 100% 94%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.85rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 173 80% 40%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground relative;
  }
}

@layer components {
  .tag {
    @apply px-3 py-1 text-xs font-medium rounded-full bg-secondary text-gray-700;
  }

  .section-title {
    @apply text-3xl font-bold mb-6 md:text-4xl;
  }

  .section-subtitle {
    @apply text-lg text-gray-600 mb-10 max-w-3xl;
  }

  .card {
    @apply bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md;
  }

  .numbered-item {
    @apply relative;
  }

  .numbered-item::before {
    @apply content-[attr(data-number)] absolute -top-2 -left-2 text-6xl font-bold text-gray-100 z-0 pointer-events-none;
  }

  /* Responsive container classes */
  .container-responsive {
    @apply w-full px-4 mx-auto sm:px-6 md:px-8;
    max-width: 100%;
  }

  @screen sm {
    .container-responsive {
      max-width: 640px;
    }
  }

  @screen md {
    .container-responsive {
      max-width: 768px;
    }
  }

  @screen lg {
    .container-responsive {
      max-width: 1024px;
    }
  }

  @screen xl {
    .container-responsive {
      max-width: 1280px;
    }
  }

  /* Playground specific components */
  .playground-panel {
    @apply bg-card rounded-lg border shadow-sm overflow-hidden;
  }

  .playground-card {
    @apply bg-card rounded-lg border p-4 shadow-sm hover:shadow-md transition-all duration-300;
  }

  /* Responsive typography */
  .text-responsive-xl {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold;
  }

  .text-responsive-lg {
    @apply text-xl md:text-2xl lg:text-3xl font-bold;
  }

  .text-responsive-md {
    @apply text-lg md:text-xl font-medium;
  }

  .text-responsive-sm {
    @apply text-sm md:text-base;
  }

  /* Performance-optimized animations */
  .animate-fade-in {
    animation: optimized-fade-in 0.3s ease-out forwards;
  }

  .animate-slide-up {
    animation: optimized-slide-up 0.4s ease-out forwards;
  }

  .animate-scale-in {
    animation: optimized-scale-in 0.3s ease-out forwards;
  }

  /* Scroll-optimized animations */
  .scroll-animate {
    will-change: transform, opacity;
    contain: layout paint;
    transform: translateZ(0);
  }

  /* GPU-accelerated hover effects */
  .gpu-hover {
    transform: translateZ(0);
    transition: transform 0.2s ease-out;
  }

  .gpu-hover:hover {
    transform: translateZ(0) translateY(-2px);
  }
}

/* Optimized keyframes for better performance */
@keyframes optimized-fade-in {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes optimized-slide-up {
  from {
    opacity: 0;
    transform: translateZ(0) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateZ(0) translateY(0);
  }
}

@keyframes optimized-scale-in {
  from {
    opacity: 0;
    transform: translateZ(0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateZ(0) scale(1);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .gpu-hover:hover {
    transform: none;
  }

  .scroll-animate {
    will-change: auto;
    contain: none;
    transform: none;
  }
}
